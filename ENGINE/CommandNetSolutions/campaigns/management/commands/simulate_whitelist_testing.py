"""
Django management command to simulate comprehensive whitelist testing.

This command:
1. Simulates realistic account collection based on campaign targets
2. Applies tag analysis against collected accounts
3. Generates WhiteListEntry records based on tag matching
4. Creates comprehensive test data for whitelist analytics
"""

import random
import json
from datetime import datetime, timedelta
from django.core.management.base import BaseCommand
from django.utils import timezone
from django.db import transaction

from campaigns.models import Campaign, CampaignTag, TagAnalysisResult, WorkflowExecution
from instagram.models import Accounts, WhiteListEntry


class Command(BaseCommand):
    help = 'Simulate comprehensive whitelist testing for a campaign'

    def add_arguments(self, parser):
        parser.add_argument(
            'campaign_id',
            type=str,
            help='Campaign ID to simulate'
        )
        parser.add_argument(
            '--num-accounts',
            type=int,
            default=150,
            help='Number of accounts to simulate (default: 150)'
        )
        parser.add_argument(
            '--clear-existing',
            action='store_true',
            help='Clear existing accounts and whitelist entries for this campaign'
        )

    def handle(self, *args, **options):
        campaign_id = options['campaign_id']
        num_accounts = options['num_accounts']
        clear_existing = options['clear_existing']

        self.stdout.write(
            self.style.SUCCESS(f'Starting whitelist testing simulation for campaign {campaign_id}')
        )

        try:
            campaign = Campaign.objects.get(id=campaign_id)
        except Campaign.DoesNotExist:
            self.stdout.write(
                self.style.ERROR(f'Campaign {campaign_id} does not exist')
            )
            return

        if clear_existing:
            self.clear_existing_data(campaign)

        with transaction.atomic():
            # Step 1: Simulate account collection
            accounts = self.simulate_account_collection(campaign, num_accounts)

            # Step 2: Apply tag analysis
            tag_results = self.apply_tag_analysis(campaign, accounts)

            # Step 3: Generate whitelist entries
            whitelist_entries = self.generate_whitelist_entries(campaign, accounts, tag_results)

            # Step 4: Create workflow execution records
            self.create_workflow_records(campaign, len(accounts), len(whitelist_entries))

        self.stdout.write(
            self.style.SUCCESS(
                f'Simulation completed:\n'
                f'- Created {len(accounts)} accounts\n'
                f'- Generated {len(tag_results)} tag analysis results\n'
                f'- Created {len(whitelist_entries)} whitelist entries\n'
                f'- Campaign status updated to completed'
            )
        )

    def clear_existing_data(self, campaign):
        """Clear existing simulation data for the campaign"""
        self.stdout.write('Clearing existing data...')

        # Delete in order of foreign key dependencies
        TagAnalysisResult.objects.filter(campaign=campaign).delete()
        WhiteListEntry.objects.filter(account__campaign_id=str(campaign.id)).delete()
        Accounts.objects.filter(campaign_id=str(campaign.id)).delete()
        WorkflowExecution.objects.filter(campaign_id=str(campaign.id)).delete()

    def simulate_account_collection(self, campaign, num_accounts):
        """Simulate realistic account collection based on campaign targets"""
        self.stdout.write(f'Simulating collection of {num_accounts} accounts...')

        accounts = []

        # Get campaign targets for context
        username_targets = campaign.username_targets.all()
        location_targets = campaign.location_targets.all()

        # Define account templates based on campaign type
        if 'fitness' in campaign.name.lower():
            account_templates = self.get_fitness_account_templates()
        elif 'fashion' in campaign.name.lower():
            account_templates = self.get_fashion_account_templates()
        elif 'tech' in campaign.name.lower():
            account_templates = self.get_tech_account_templates()
        else:
            account_templates = self.get_mixed_account_templates()

        for i in range(num_accounts):
            template = random.choice(account_templates)

            account = Accounts.objects.create(
                username=f"{template['username_base']}_{i+1}",
                full_name=f"{template['username_base'].replace('_', ' ').title()} {i+1}",
                bio=random.choice(template['bios']),
                followers=random.randint(template['followers_min'], template['followers_max']),
                following=random.randint(template['following_min'], template['following_max']),
                number_of_posts=random.randint(template['posts_min'], template['posts_max']),
                interests=random.choice(template['interests']),
                account_type=random.choice(template['account_types']),
                is_verified=random.random() < template['verified_rate'],
                campaign_id=str(campaign.id),
                collection_date=timezone.now() - timedelta(days=random.randint(1, 30)),
                locations=random.sample(template['locations'], random.randint(1, 3))
            )
            accounts.append(account)

        return accounts

    def get_fitness_account_templates(self):
        """Get fitness-specific account templates"""
        return [
            {
                'username_base': 'fitness_coach',
                'bios': [
                    "Certified personal trainer 💪 Helping you reach your fitness goals #fitness #gym",
                    "Fitness coach | Transformation specialist | DM for training plans 🏋️‍♂️",
                    "NASM certified trainer | Nutrition coach | Online coaching available 💊",
                ],
                'followers_min': 5000, 'followers_max': 100000,
                'following_min': 500, 'following_max': 3000,
                'posts_min': 200, 'posts_max': 1000,
                'interests': [['fitness', 'health', 'nutrition'], ['fitness', 'gym', 'strength']],
                'account_types': ['creator', 'business', 'personal'],
                'verified_rate': 0.15,
                'locations': ['Los Angeles', 'Miami', 'New York', 'Austin']
            },
            {
                'username_base': 'yoga_instructor',
                'bios': [
                    "Yoga instructor 🧘‍♀️ Mind-body wellness | Online classes available",
                    "RYT 200 certified | Mindfulness coach | Retreat leader 🌱",
                    "Yoga teacher | Meditation guide | Holistic wellness advocate ✨",
                ],
                'followers_min': 2000, 'followers_max': 50000,
                'following_min': 300, 'following_max': 2000,
                'posts_min': 150, 'posts_max': 800,
                'interests': [['yoga', 'mindfulness', 'wellness'], ['fitness', 'meditation', 'health']],
                'account_types': ['creator', 'personal'],
                'verified_rate': 0.08,
                'locations': ['Los Angeles', 'San Francisco', 'Portland', 'Boulder']
            }
        ]

    def get_fashion_account_templates(self):
        """Get fashion-specific account templates"""
        return [
            {
                'username_base': 'fashion_blogger',
                'bios': [
                    "Fashion blogger ✨ Daily outfit inspiration | Shop my looks ⬇️ #fashion #style",
                    "Style influencer | Fashion tips & trends | Collaboration inquiries: DM 💄",
                    "Fashion enthusiast | Outfit posts | Style inspiration daily 👗",
                ],
                'followers_min': 10000, 'followers_max': 500000,
                'following_min': 1000, 'following_max': 5000,
                'posts_min': 300, 'posts_max': 2000,
                'interests': [['fashion', 'style', 'beauty'], ['fashion', 'luxury', 'designer']],
                'account_types': ['creator', 'business', 'personal'],
                'verified_rate': 0.12,
                'locations': ['Milan', 'Paris', 'New York', 'London']
            },
            {
                'username_base': 'style_consultant',
                'bios': [
                    "Personal stylist | Fashion consultant | Making style accessible 💫",
                    "Professional stylist | Wardrobe consultant | Book consultations via DM 📧",
                    "Style expert | Personal shopping | Fashion advice | DM for services 🛍️",
                ],
                'followers_min': 5000, 'followers_max': 80000,
                'following_min': 800, 'following_max': 3000,
                'posts_min': 200, 'posts_max': 1200,
                'interests': [['fashion', 'style', 'consulting'], ['fashion', 'business', 'personal']],
                'account_types': ['business', 'creator'],
                'verified_rate': 0.10,
                'locations': ['New York', 'Los Angeles', 'Chicago', 'Miami']
            }
        ]

    def get_tech_account_templates(self):
        """Get tech-specific account templates"""
        return [
            {
                'username_base': 'tech_reviewer',
                'bios': [
                    "Tech reviewer | Latest gadgets & reviews | YouTube: TechReviews 📱",
                    "Technology enthusiast | Product reviews | Tech news & updates 💻",
                    "Gadget reviewer | Tech tips | Unboxing videos | Subscribe for more! 🔔",
                ],
                'followers_min': 15000, 'followers_max': 200000,
                'following_min': 500, 'following_max': 2000,
                'posts_min': 100, 'posts_max': 800,
                'interests': [['technology', 'gadgets', 'reviews'], ['tech', 'innovation', 'startup']],
                'account_types': ['creator', 'business'],
                'verified_rate': 0.18,
                'locations': ['San Francisco', 'Seattle', 'Austin', 'Boston']
            }
        ]

    def get_mixed_account_templates(self):
        """Get mixed account templates for general campaigns"""
        return self.get_fitness_account_templates() + self.get_fashion_account_templates() + self.get_tech_account_templates()

    def apply_tag_analysis(self, campaign, accounts):
        """Apply tag analysis to collected accounts"""
        self.stdout.write('Applying tag analysis...')

        # Get campaign tags
        campaign_tags = CampaignTag.objects.filter(campaign=campaign)
        if not campaign_tags.exists():
            self.stdout.write(self.style.WARNING('No campaign tags found. Creating default tags...'))
            campaign_tags = self.create_default_tags(campaign)

        tag_results = []

        for account in accounts:
            for campaign_tag in campaign_tags:
                tag = campaign_tag.tag

                # Simulate tag matching based on tag conditions
                matches = self.evaluate_tag_conditions(account, tag)

                if matches:
                    confidence = random.uniform(0.7, 1.0)  # High confidence for matches

                    result = TagAnalysisResult.objects.create(
                        campaign=campaign,
                        account_id=account.username,
                        tag=tag,
                        matched=True,
                        confidence_score=confidence,
                        match_details={
                            'matched_conditions': matches,
                            'account_score': confidence * 100,
                            'tag_weight': campaign_tag.weight if hasattr(campaign_tag, 'weight') else 1.0
                        }
                    )
                    tag_results.append(result)

        return tag_results

    def evaluate_tag_conditions(self, account, tag):
        """Evaluate if an account matches tag conditions"""
        matched_conditions = []

        # Get tag conditions (simplified simulation)
        if hasattr(tag, 'conditions_json') and tag.conditions_json:
            try:
                conditions = json.loads(tag.conditions_json)
            except (json.JSONDecodeError, TypeError):
                conditions = []
        else:
            conditions = []

        # If no conditions, create default conditions based on tag name
        if not conditions:
            conditions = self.create_default_conditions_for_tag(tag)

        for condition in conditions:
            if self.check_condition(account, condition):
                matched_conditions.append(condition)

        return matched_conditions

    def check_condition(self, account, condition):
        """Check if an account meets a specific condition"""
        field = condition.get('field', '')
        operator = condition.get('operator', 'contains')
        value = condition.get('value', '')

        if field == 'followers':
            if operator == 'gte':
                return account.followers >= int(value)
            elif operator == 'lte':
                return account.followers <= int(value)
            elif operator == 'between':
                min_val, max_val = map(int, value.split(','))
                return min_val <= account.followers <= max_val

        elif field == 'bio':
            if operator == 'contains':
                return value.lower() in account.bio.lower()
            elif operator == 'contains_any':
                keywords = [kw.strip().lower() for kw in value.split(',')]
                return any(kw in account.bio.lower() for kw in keywords)

        elif field == 'account_type':
            return account.account_type == value

        elif field == 'is_verified':
            return account.is_verified == (value.lower() == 'true')

        elif field == 'interests':
            if operator == 'contains_any':
                target_interests = [interest.strip().lower() for interest in value.split(',')]
                account_interests = [interest.lower() for interest in account.interests]
                return any(target in account_interests for target in target_interests)

        return False

    def create_default_conditions_for_tag(self, tag):
        """Create default conditions based on tag name"""
        tag_name = tag.name.lower()

        if 'influencer' in tag_name:
            return [{'field': 'followers', 'operator': 'gte', 'value': '10000'}]
        elif 'verified' in tag_name:
            return [{'field': 'is_verified', 'operator': 'equals', 'value': 'true'}]
        elif 'fitness' in tag_name:
            return [{'field': 'bio', 'operator': 'contains_any', 'value': 'fitness,gym,workout,trainer'}]
        elif 'fashion' in tag_name:
            return [{'field': 'bio', 'operator': 'contains_any', 'value': 'fashion,style,outfit,designer'}]
        elif 'tech' in tag_name:
            return [{'field': 'bio', 'operator': 'contains_any', 'value': 'tech,technology,gadget,review'}]
        elif 'business' in tag_name:
            return [{'field': 'account_type', 'operator': 'equals', 'value': 'business'}]
        elif 'creator' in tag_name:
            return [{'field': 'account_type', 'operator': 'equals', 'value': 'creator'}]
        else:
            return [{'field': 'followers', 'operator': 'gte', 'value': '1000'}]

    def create_default_tags(self, campaign):
        """Create default tags for testing if none exist"""
        from campaigns.models import DynamicTag

        # Create default tags based on campaign type
        default_tags = [
            {'name': 'High Follower Count', 'category': 'Audience Size'},
            {'name': 'Verified Account', 'category': 'Account Quality'},
            {'name': 'Business Account', 'category': 'Account Type'},
            {'name': 'Creator Account', 'category': 'Account Type'},
        ]

        # Add campaign-specific tags
        if 'fitness' in campaign.name.lower():
            default_tags.extend([
                {'name': 'Fitness Influencer', 'category': 'Fitness'},
                {'name': 'Personal Trainer', 'category': 'Fitness'},
            ])
        elif 'fashion' in campaign.name.lower():
            default_tags.extend([
                {'name': 'Fashion Blogger', 'category': 'Fashion'},
                {'name': 'Style Influencer', 'category': 'Fashion'},
            ])
        elif 'tech' in campaign.name.lower():
            default_tags.extend([
                {'name': 'Tech Reviewer', 'category': 'Technology'},
                {'name': 'Tech Enthusiast', 'category': 'Technology'},
            ])

        campaign_tags = []
        for tag_data in default_tags:
            tag, created = DynamicTag.objects.get_or_create(
                name=tag_data['name'],
                defaults={
                    'description': f"Auto-generated tag for {tag_data['name']}",
                    'tag_type': 'keyword',
                    'is_global': True,
                    'is_system': True
                }
            )

            campaign_tag, created = CampaignTag.objects.get_or_create(
                campaign=campaign,
                tag=tag
            )
            campaign_tags.append(campaign_tag)

        return campaign_tags

    def generate_whitelist_entries(self, campaign, accounts, tag_results):
        """Generate whitelist entries based on tag analysis results"""
        self.stdout.write('Generating whitelist entries...')

        # Get accounts that matched at least one tag
        tagged_account_usernames = set(result.account_id for result in tag_results)
        tagged_accounts = [acc for acc in accounts if acc.username in tagged_account_usernames]

        # Select a percentage of tagged accounts for whitelisting (70-90%)
        whitelist_rate = random.uniform(0.7, 0.9)
        num_to_whitelist = int(len(tagged_accounts) * whitelist_rate)

        # Sort by followers and select top accounts plus some random ones
        tagged_accounts.sort(key=lambda x: x.followers, reverse=True)
        top_accounts = tagged_accounts[:int(num_to_whitelist * 0.6)]  # Top 60%
        remaining_accounts = tagged_accounts[int(num_to_whitelist * 0.6):]
        random_accounts = random.sample(remaining_accounts, min(len(remaining_accounts), num_to_whitelist - len(top_accounts)))

        accounts_to_whitelist = top_accounts + random_accounts

        whitelist_entries = []
        for account in accounts_to_whitelist:
            # Determine privileges based on account characteristics
            privileges = self.determine_privileges(account, tag_results)

            entry = WhiteListEntry.objects.create(
                account=account,
                dm=privileges['dm'],
                follow=privileges['follow'],
                comment=privileges['comment'],
                post_like=privileges['post_like'],
                discover=privileges['discover'],
                favorite=privileges['favorite']
            )
            whitelist_entries.append(entry)

        return whitelist_entries

    def determine_privileges(self, account, tag_results):
        """Determine privileges for an account based on its characteristics"""
        # Get tag results for this account
        account_tags = [result for result in tag_results if result.account_id == account.username]

        # Base privilege probabilities
        base_probs = {
            'dm': 0.3,
            'follow': 0.8,
            'comment': 0.6,
            'post_like': 0.9,
            'discover': 0.4,
            'favorite': 0.5
        }

        # Adjust probabilities based on account characteristics
        multiplier = 1.0

        # Higher follower count = more privileges
        if account.followers > 100000:
            multiplier += 0.4
        elif account.followers > 50000:
            multiplier += 0.3
        elif account.followers > 10000:
            multiplier += 0.2

        # Verified accounts get more privileges
        if account.is_verified:
            multiplier += 0.3

        # Business/Creator accounts get more privileges
        if account.account_type in ['business', 'creator']:
            multiplier += 0.2

        # Multiple tag matches = more privileges
        if len(account_tags) > 2:
            multiplier += 0.2
        elif len(account_tags) > 1:
            multiplier += 0.1

        # Apply multiplier and randomness
        privileges = {}
        for privilege, base_prob in base_probs.items():
            adjusted_prob = min(base_prob * multiplier, 0.95)  # Cap at 95%
            privileges[privilege] = random.random() < adjusted_prob

        return privileges

    def create_workflow_records(self, campaign, num_accounts, num_whitelist):
        """Create workflow execution records for tracking"""
        self.stdout.write('Creating workflow execution records...')

        # Create collection workflow
        collection_workflow = WorkflowExecution.objects.create(
            campaign_id=str(campaign.id),
            workflow_name='account_collection_simulation',
            workflow_path='/simulated/account_collection.pygraph',
            workflow_type='collection',
            status='completed',
            start_time=timezone.now() - timedelta(hours=2),
            end_time=timezone.now() - timedelta(hours=1),
            duration=3600,  # 1 hour
            total_items=num_accounts,
            processed_items=num_accounts,
            successful_items=num_accounts,
            failed_items=0,
            progress=100.0
        )

        # Create analysis workflow
        analysis_workflow = WorkflowExecution.objects.create(
            campaign_id=str(campaign.id),
            workflow_name='tag_analysis_simulation',
            workflow_path='/simulated/tag_analysis.pygraph',
            workflow_type='analysis',
            status='completed',
            start_time=timezone.now() - timedelta(hours=1),
            end_time=timezone.now() - timedelta(minutes=30),
            duration=1800,  # 30 minutes
            total_items=num_accounts,
            processed_items=num_accounts,
            successful_items=num_whitelist,
            failed_items=0,
            progress=100.0
        )

        # Update campaign status
        campaign.status = 'completed'
        campaign.save()

        return [collection_workflow, analysis_workflow]
