"""
Management command to create a test campaign.

This script:
1. Creates a test campaign
2. Adds location targets to the campaign
3. Adds username targets to the campaign
4. Associates tags with the campaign

Usage:
    python manage.py create_test_campaign
"""
import uuid
import logging
from django.core.management.base import BaseCommand
from django.db import transaction
from django.contrib.auth.models import User
from django.utils import timezone

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = 'Create a test campaign'

    def add_arguments(self, parser):
        parser.add_argument(
            '--name',
            type=str,
            default='Test Campaign',
            help='Name of the test campaign'
        )
        parser.add_argument(
            '--description',
            type=str,
            default='A test campaign created for testing purposes',
            help='Description of the test campaign'
        )
        parser.add_argument(
            '--target-type',
            type=str,
            choices=['location', 'username', 'mixed'],
            default='mixed',
            help='Target type for the campaign'
        )

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('Creating test campaign...'))
        
        campaign_name = options['name']
        campaign_description = options['description']
        target_type = options['target_type']
        
        try:
            # Import models
            from campaigns.models import (
                Campaign, LocationTarget, UsernameTarget, DynamicTag,
                TagGroup, TagCategory, CampaignTag
            )
            from django.contrib.auth.models import User
            
            with transaction.atomic():
                # Get or create admin user
                admin_user, created = User.objects.get_or_create(
                    username='admin',
                    defaults={
                        'is_staff': True,
                        'is_superuser': True,
                        'email': '<EMAIL>'
                    }
                )
                
                # Create campaign
                campaign = Campaign.objects.create(
                    name=campaign_name,
                    description=campaign_description,
                    target_type=target_type,
                    audience_type='both',
                    status='completed',  # Set to completed so it can be used in CEP
                    creator=admin_user
                )
                
                self.stdout.write(f'Created campaign: {campaign.name} (ID: {campaign.id})')
                
                # Add location targets
                if target_type in ['location', 'mixed']:
                    self.stdout.write('Adding location targets...')
                    
                    LocationTarget.objects.create(
                        campaign=campaign,
                        country='United States',
                        city='New York',
                        location_id='123456789'
                    )
                    
                    LocationTarget.objects.create(
                        campaign=campaign,
                        country='United States',
                        city='Los Angeles',
                        location_id='987654321'
                    )
                    
                    LocationTarget.objects.create(
                        campaign=campaign,
                        country='United Kingdom',
                        city='London',
                        location_id='456789123'
                    )
                
                # Add username targets
                if target_type in ['username', 'mixed']:
                    self.stdout.write('Adding username targets...')
                    
                    UsernameTarget.objects.create(
                        campaign=campaign,
                        username='fitness_influencer',
                        audience_type='followers'
                    )
                    
                    UsernameTarget.objects.create(
                        campaign=campaign,
                        username='travel_blogger',
                        audience_type='followers'
                    )
                    
                    UsernameTarget.objects.create(
                        campaign=campaign,
                        username='tech_reviewer',
                        audience_type='followers'
                    )
                
                # Associate tags with campaign
                self.stdout.write('Associating tags with campaign...')
                
                # Get some tags from different categories
                fitness_tags = DynamicTag.objects.filter(category__name='Fitness')[:2]
                travel_tags = DynamicTag.objects.filter(category__name='Travel')[:2]
                tech_tags = DynamicTag.objects.filter(category__name='Technology')[:2]
                engagement_tags = DynamicTag.objects.filter(category__name='Engagement')[:2]
                
                # Associate tags with campaign
                for tag in list(fitness_tags) + list(travel_tags) + list(tech_tags) + list(engagement_tags):
                    CampaignTag.objects.create(
                        campaign=campaign,
                        tag=tag,
                        is_required=(tag in fitness_tags)  # Make fitness tags required
                    )
                
                self.stdout.write(self.style.SUCCESS(f'Successfully created test campaign: {campaign.name} (ID: {campaign.id})'))
                
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'Error creating test campaign: {str(e)}'))
            logger.exception('Error creating test campaign')
            raise
