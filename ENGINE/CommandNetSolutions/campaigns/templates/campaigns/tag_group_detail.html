{% extends "campaigns/base.html" %}

{% block title %}{{ tag_group.name }} - Tag Group{% endblock %}

{% block campaign_content %}
<div class="container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="page-title">{{ tag_group.name }}</h1>
            <p class="page-subtitle">{{ tag_group.description|default:"No description" }}</p>
        </div>
        <div>
            <a href="{% url 'campaigns:tag_group_list' %}" class="btn btn-outline-secondary me-2">
                <i class="fas fa-arrow-left me-1"></i> Back
            </a>
            <a href="{% url 'campaigns:tag_group_update' tag_group.id %}" class="btn btn-primary">
                <i class="fas fa-edit me-1"></i> Edit Tag Group
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-md-4">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-info-circle me-2"></i>Tag Group Information</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label class="form-label fw-bold">Name</label>
                        <p>{{ tag_group.name }}</p>
                    </div>
                    <div class="mb-3">
                        <label class="form-label fw-bold">Description</label>
                        <p>{{ tag_group.description|default:"No description" }}</p>
                    </div>
                    <div class="mb-3">
                        <label class="form-label fw-bold">Global</label>
                        <p>
                            {% if tag_group.is_global %}
                            <span class="badge bg-success">Yes</span>
                            {% else %}
                            <span class="badge bg-secondary">No</span>
                            {% endif %}
                        </p>
                    </div>
                    <div class="mb-3">
                        <label class="form-label fw-bold">Created</label>
                        <p>{{ tag_group.created_at|date:"M d, Y H:i" }}</p>
                    </div>
                    <div class="mb-3">
                        <label class="form-label fw-bold">Last Updated</label>
                        <p>{{ tag_group.updated_at|date:"M d, Y H:i" }}</p>
                    </div>
                    <div class="mb-3">
                        <label class="form-label fw-bold">Created By</label>
                        <p>{{ tag_group.creator.username|default:"System" }}</p>
                    </div>
                </div>
            </div>


        </div>

        <div class="col-md-8">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-plus-circle me-2"></i>Add Tags to Group</h5>
                </div>
                <div class="card-body">
                    <form action="{% url 'campaigns:add_tag_to_group' tag_group.id %}" method="post">
                        {% csrf_token %}
                        <div class="d-flex">
                            <div class="flex-grow-1 me-2">
                                <label for="tag_id" class="form-label">Select Tag</label>
                                <select name="tag_id" id="tag_id" class="form-select" required>
                                    <option value="">-- Select a tag --</option>
                                    {% for tag in available_tags %}
                                    <option value="{{ tag.id }}">{{ tag.name }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                            <div class="d-flex align-items-end">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-plus-circle me-1"></i> Add
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="fas fa-tags me-2"></i>Tags in this Group</h5>
                    <div class="d-flex">
                        <div class="me-2">
                            <select id="categoryFilter" class="form-select form-select-sm">
                                <option value="">All Categories</option>
                                {% for category in categories %}
                                <option value="{{ category.name }}">{{ category.name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="search-box">
                            <i class="fas fa-search"></i>
                            <input type="text" class="form-control" id="searchTags" placeholder="Search tags...">
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    {% if tags %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Name</th>
                                    <th>Tag Description</th>
                                    <th>Tag Category</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for tag in tags %}
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="me-3">
                                                <i class="fas fa-tag text-primary"></i>
                                            </div>
                                            <div>
                                                <div class="fw-bold">{{ tag.name }}</div>
                                            </div>
                                        </div>
                                    </td>
                                    <td>{{ tag.description|default:"" }}</td>
                                    <td>{{ tag.category.name|default:"Uncategorized" }}</td>
                                    <td>
                                        <div class="btn-group action-buttons">
                                            <a href="{% url 'campaigns:dynamic_tag_update' tag.id %}" class="btn btn-sm btn-secondary" data-bs-toggle="tooltip" title="Edit Tag">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <a href="{% url 'campaigns:remove_tag_from_group' tag_group.id tag.id %}" class="btn btn-sm btn-danger" data-bs-toggle="tooltip" title="Remove from Group">
                                                <i class="fas fa-times"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="empty-state">
                        <div class="empty-state-icon">
                            <i class="fas fa-tag"></i>
                        </div>
                        <h4>No Tags in this Group</h4>
                        <p>This tag group doesn't have any tags yet.</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.getElementById('searchTags');
    const categoryFilter = document.getElementById('categoryFilter');
    const rows = document.querySelectorAll('tbody tr');

    // Function to filter rows based on search term and category
    function filterRows() {
        const searchTerm = searchInput.value.toLowerCase();
        const categoryValue = categoryFilter.value;

        rows.forEach(row => {
            const text = row.textContent.toLowerCase();
            const categoryCell = row.querySelector('td:nth-child(3)').textContent.trim();

            const matchesSearch = text.includes(searchTerm);
            const matchesCategory = !categoryValue || categoryCell === categoryValue;

            if (matchesSearch && matchesCategory) {
                row.style.display = '';
            } else {
                row.style.display = 'none';
            }
        });
    }

    // Search functionality
    if (searchInput) {
        searchInput.addEventListener('keyup', filterRows);
    }

    // Category filter functionality
    if (categoryFilter) {
        categoryFilter.addEventListener('change', filterRows);
    }

    // Initialize tooltips
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
});
</script>
{% endblock %}
