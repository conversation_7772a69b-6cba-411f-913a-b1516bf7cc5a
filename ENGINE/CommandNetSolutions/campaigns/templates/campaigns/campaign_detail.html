{% extends "campaigns/base.html" %}
{% load campaign_tags %}

{% block title %}{{ campaign.name }}{% endblock %}

{% block campaign_content %}
<div class="d-flex justify-content-between align-items-start mb-4">
    <div>
        <div class="d-flex align-items-center">
            <h1 class="page-title me-3">{{ campaign.name }}</h1>
            <span class="campaign-status status-{{ campaign.status }}">
                {{ campaign.get_status_display }}
            </span>
            {% if campaign.is_favorite %}
            <span class="ms-2 badge bg-warning" title="Favorite Campaign">
                <i class="fas fa-star"></i> Favorite
            </span>
            {% endif %}
        </div>
        <p class="page-subtitle">{{ campaign.description|default:"No description provided" }}</p>
    </div>
    <div class="campaign-detail-buttons-fix">
        <div class="btn-group detail-action-buttons">
            {% if campaign.status == 'draft' %}
            <a href="{% url 'campaigns:campaign_update' campaign.id %}" class="btn btn-secondary btn-icon edit-button">
                <i class="fas fa-edit me-1"></i> <span>Edit</span>
            </a>
            <a href="{% url 'campaigns:launch_campaign' campaign.id %}" class="btn btn-success btn-icon launch-campaign-btn">
                <i class="fas fa-rocket me-1"></i> <span>Launch Campaign</span>
            </a>
            {% endif %}

            {% if campaign.status == 'running' %}
            <a href="#" class="btn btn-warning btn-icon campaign-action-btn" data-action="pause" data-campaign-id="{{ campaign.id }}" data-campaign-name="{{ campaign.name }}">
                <i class="fas fa-pause me-1"></i>
                <!-- <span>Pause</span> -->
            </a>
            <a href="#" class="btn btn-danger btn-icon campaign-action-btn" data-action="stop" data-campaign-id="{{ campaign.id }}" data-campaign-name="{{ campaign.name }}">
                <i class="fas fa-stop me-1"></i>
                 <!-- <span>Stop</span> -->
            </a>
            {% endif %}

            {% if campaign.status == 'paused' %}
            <a href="#" class="btn btn-success btn-icon campaign-action-btn" data-action="resume" data-campaign-id="{{ campaign.id }}" data-campaign-name="{{ campaign.name }}">
                <i class="fas fa-play me-1"></i>
                 <!-- <span>Resume Campaign</span> -->
            </a>
            <a href="#" class="btn btn-danger btn-icon campaign-action-btn" data-action="stop" data-campaign-id="{{ campaign.id }}" data-campaign-name="{{ campaign.name }}">
                <i class="fas fa-stop me-1"></i>
                <!-- <span>Stop Campaign</span> -->
            </a>
            {% endif %}

            {% if campaign.status == 'pending' %}
            <a href="#" class="btn btn-danger btn-icon campaign-action-btn" data-action="stop" data-campaign-id="{{ campaign.id }}" data-campaign-name="{{ campaign.name }}">
                <i class="fas fa-stop me-1"></i>
                 <!-- <span>Stop Campaign</span> -->
            </a>
            {% endif %}

            <a href="{% url 'campaigns:toggle_favorite' campaign.id %}?next={{ request.path|urlencode }}" class="btn {% if campaign.is_favorite %}btn-warning{% else %}btn-outline-warning{% endif %} btn-icon favorite-button">
                <i class="fas fa-star me-1"></i>
                 <!-- <span>{% if campaign.is_favorite %}UnFavorite{% else %}Favorite{% endif %}</span> -->
            </a>
            {% if campaign.status != 'draft' %}
            <div class="btn-group">
                <button type="button" class="btn btn-info dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false">
                    <i class="fas fa-file-export me-1"></i> <span>Export</span>
                </button>
                <ul class="dropdown-menu dropdown-menu-end">
                    <li><h6 class="dropdown-header">Accounts Export</h6></li>
                    <li><a class="dropdown-item" href="{% url 'campaigns:export_campaign' campaign.id %}?type=accounts&format=csv">Export Accounts as CSV</a></li>
                    <li><a class="dropdown-item" href="{% url 'campaigns:export_campaign' campaign.id %}?type=accounts&format=excel">Export Accounts as Excel</a></li>
                    <li><hr class="dropdown-divider"></li>
                    <li><h6 class="dropdown-header">Whitelist Export</h6></li>
                    <li><a class="dropdown-item" href="{% url 'campaigns:export_campaign' campaign.id %}?type=whitelist&format=csv">Export Whitelist as CSV</a></li>
                </ul>
            </div>
            {% endif %}
            <a href="{% url 'campaigns:campaign_list' %}" class="btn btn-outline-primary btn-icon back-button">
                <i class="fas fa-arrow-left me-1"></i> <span>Back to List</span>
            </a>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-10 mx-auto">
        {% if campaign.status != 'draft' %}
        <!-- Campaign Progress -->
        <div class="campaign-card mb-4">
            <div class="card-header">
                <h5 class="mb-0 px-2"><i class="fas fa-chart-bar me-2"></i>Campaign Progress</h5>
            </div>
            <div class="card-body">
                <div class="row mb-4">
                    <div class="col-md-4">
                        <div class="stats-card primary">
                            <div class="stats-icon">
                                <i class="fas fa-users"></i>
                            </div>
                            <div class="stats-title">Total Accounts Found</div>
                            <div class="stats-value">{{ result.total_accounts_found }}</div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="stats-card secondary">
                            <div class="stats-icon">
                                <i class="fas fa-check-circle"></i>
                            </div>
                            <div class="stats-title">Accounts Processed</div>
                            <div class="stats-value">{{ result.total_accounts_processed }}</div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="stats-card info">
                            <div class="stats-icon">
                                <i class="fas fa-percentage"></i>
                            </div>
                            <div class="stats-title">Progress</div>
                            <div class="stats-value">
                                {% if result.total_accounts_found > 0 %}
                                    {% with progress=result.total_accounts_processed total=result.total_accounts_found %}
                                    {% with percentage=progress|floatformat:0|multiply:100|divisibleby:total|floatformat:0|default:0 %}
                                        {{ percentage }}%
                                    {% endwith %}
                                    {% endwith %}
                                {% else %}
                                    0%
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>

                <div class="mb-4 p-2">
                    <div class="d-flex justify-content-between mb-3">
                        <div class="fw-bold">Overall Progress</div>
                        <div>{{ result.total_accounts_processed }} / {{ result.total_accounts_found }}</div>
                    </div>
                    <div class="progress mb-3">
                        {% if result.total_accounts_found > 0 %}
                        {% with progress=result.total_accounts_processed total=result.total_accounts_found %}
                        {% with percentage=progress|floatformat:0|multiply:100|divisibleby:total|floatformat:0|default:0 %}
                        <div class="progress-bar" role="progressbar" style="width: {{ percentage }}%" aria-valuenow="{{ percentage }}" aria-valuemin="0" aria-valuemax="100"></div>
                        {% endwith %}
                        {% endwith %}
                        {% else %}
                        <div class="progress-bar" role="progressbar" style="width: 0%" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100"></div>
                        {% endif %}
                    </div>
                </div>

                <div class="alert alert-info mt-4">
                    <i class="fas fa-info-circle me-2"></i>
                    {% if result.last_processed_at %}
                    Last processed at {{ result.last_processed_at|date:"F j, Y, g:i a" }}
                    {% else %}
                    Processing has not started yet.
                    {% endif %}
                </div>
            </div>
        </div>
        {% else %}
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="stats-card primary">
                    <div class="stats-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="stats-title">Total Accounts Found</div>
                    <div class="stats-value">{{ result.total_accounts_found }}</div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="stats-card secondary">
                    <div class="stats-icon">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <div class="stats-title">Accounts Processed</div>
                    <div class="stats-value">{{ result.total_accounts_processed }}</div>
                </div>
            </div>
        </div>
        {% endif %}

        <!-- Campaign Details -->
        <div class="campaign-card mb-4">
            <div class="card-header">
                <h5 class="mb-0 px-2"><i class="fas fa-info-circle me-2"></i>Campaign Details</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="detail-section">
                            <div class="detail-item">
                                <div class="detail-label">Target Type</div>
                                <div class="detail-value">
                                    {% with has_location=campaign.location_targets.exists has_username=campaign.username_targets.exists %}
                                    {% if has_location and has_username %}
                                        <span class="badge bg-light text-dark">Mixed (Both Types)</span>
                                    {% elif has_location %}
                                        <span class="badge bg-light text-dark">Location Based</span>
                                    {% elif has_username %}
                                        <span class="badge bg-light text-dark">Username Based</span>
                                    {% else %}
                                        <span class="badge bg-light text-dark">{{ campaign.get_target_type_display }}</span>
                                    {% endif %}
                                    {% endwith %}
                                </div>
                            </div>

                            <div class="detail-item">
                                <div class="detail-label">Audience Type</div>
                                <div class="detail-value">
                                    <i class="fas fa-users me-2"></i>
                                    {{ campaign.get_audience_type_display }}
                                </div>
                            </div>

                            <div class="detail-item">
                                <div class="detail-label">Created</div>
                                <div class="detail-value">
                                    <i class="fas fa-calendar-alt me-2"></i>
                                    {{ campaign.created_at|date:"F j, Y, g:i a" }}
                                </div>
                            </div>

                            <div class="detail-item">
                                <div class="detail-label">Last Updated</div>
                                <div class="detail-value">
                                    <i class="fas fa-clock me-2"></i>
                                    {{ campaign.updated_at|date:"F j, Y, g:i a" }}
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="detail-section">
                            <!-- Campaign Tags -->
                            <div class="detail-section-title mt-3">Campaign Tags</div>
                            <div class="detail-item">
                                <div class="detail-label">Assigned Tags</div>
                                <div class="detail-value">
                                    {% if campaign_tags %}
                                        {% for campaign_tag in campaign_tags %}
                                            <span class="badge bg-primary me-1 mb-1">
                                                {{ campaign_tag.tag.name }}
                                                {% if campaign_tag.is_required %}
                                                <i class="fas fa-check-circle ms-1" title="Required for whitelist"></i>
                                                {% endif %}
                                            </span>
                                        {% endfor %}
                                    {% else %}
                                        <span class="text-muted">No tags assigned</span>
                                    {% endif %}
                                </div>
                            </div>

                            {% if campaign.airflow_dag_id or campaign.airflow_run_id %}
                            <div class="detail-section-title mt-3">Airflow Information</div>

                            {% if campaign.airflow_dag_id %}
                            <div class="detail-item">
                                <div class="detail-label">Airflow DAG</div>
                                <div class="detail-value">
                                    <code>{{ campaign.airflow_dag_id }}</code>
                                </div>
                            </div>
                            {% endif %}

                            {% if campaign.airflow_run_id %}
                            <div class="detail-item">
                                <div class="detail-label">Airflow Run ID</div>
                                <div class="detail-value">
                                    {% if campaign.airflow_run_id == 'pending' %}
                                        <span class="badge bg-warning">pending</span>
                                        <small class="text-muted d-block mt-1">Waiting for Airflow to assign a run ID</small>
                                    {% elif 'manual_' in campaign.airflow_run_id %}
                                        <span class="badge bg-info">{{ campaign.airflow_run_id }}</span>
                                        <small class="text-muted d-block mt-1">Campaign is running in the background</small>
                                    {% else %}
                                        <code>{{ campaign.airflow_run_id }}</code>
                                    {% endif %}
                                </div>
                            </div>
                            {% endif %}
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Account Analysis Section -->
        <div class="campaign-card mt-4">
            <div class="card-header">
                <h5 class="mb-0 px-2"><i class="fas fa-chart-line me-2"></i>Account Analysis</h5>
            </div>
            <div class="card-body">
                <div class="mb-4 p-2">
                    <div class="d-flex justify-content-between mb-3">
                        <div class="fw-bold">Analysis Progress</div>
                        <div>{{ analysis_stats.processed }} / {{ analysis_stats.total }}</div>
                    </div>
                    <div class="progress mb-3">
                        {% if analysis_stats.total > 0 %}
                        {% with progress=analysis_stats.processed total=analysis_stats.total %}
                        {% with percentage=progress|floatformat:0|multiply:100|divisibleby:total|floatformat:0|default:0 %}
                        <div class="progress-bar" role="progressbar" style="width: {{ percentage }}%" aria-valuenow="{{ percentage }}" aria-valuemin="0" aria-valuemax="100"></div>
                        {% endwith %}
                        {% endwith %}
                        {% else %}
                        <div class="progress-bar" role="progressbar" style="width: 0%" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100"></div>
                        {% endif %}
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="stats-card primary">
                            <div class="stats-icon">
                                <i class="fas fa-check-circle"></i>
                            </div>
                            <div class="stats-title">White Listed Accounts</div>
                            <div class="stats-value">{{ analysis_stats.white_listed }}</div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="stats-card secondary">
                            <div class="stats-icon">
                                <i class="fas fa-percentage"></i>
                            </div>
                            <div class="stats-title">Conversion Rate</div>
                            <div class="stats-value">{{ analysis_stats.percentage|floatformat:1 }}%</div>
                        </div>
                    </div>
                </div>

                <div class="card-header d-flex justify-content-between align-items-center mt-4 mb-2">
                    <h5 class="mb-0">Account Management</h5>
                </div>
                <div class="btn-action-group">
                    {% if analysis_stats.total > 0 %}
                    <a href="{% url 'campaigns:campaign_accounts' campaign.id %}" class="btn btn-primary">
                        <i class="fas fa-users me-2"></i> View Collected Accounts
                    </a>
                    <a href="{% url 'campaigns:campaign_whitelist' campaign.id %}" class="btn btn-primary">
                        <i class="fas fa-star me-2"></i> View White List
                    </a>
                    <a href="{% url 'campaigns:export_campaign' campaign.id %}?type=whitelist&format=csv" class="btn btn-outline-primary">
                        <i class="fas fa-download me-2"></i> Export Whitelist
                    </a>
                    {% endif %}
                    <a href="{% url 'campaigns:campaign_tags' campaign.id %}" class="btn btn-primary">
                        <i class="fas fa-tags me-2"></i> Manage Tags
                    </a>
                    {% if analysis_stats.total == 0 %}
                    <div class="alert alert-info mt-2 mb-3">
                        <i class="fas fa-info-circle me-2"></i>
                        No accounts have been collected yet. Launch the campaign to start collecting data.
                    </div>
                    {% endif %}
                    <a href="{% url 'campaigns:analyze_campaign' campaign.id %}" class="btn btn-success" title="Analyze collected accounts using campaign tags to generate whitelist entries">
                        <i class="fas fa-search me-2"></i> Analyze Accounts
                    </a>
                    {% if analysis_stats.total > 0 %}
                    <a href="{% url 'campaigns:simulation_dashboard' campaign.id %}" class="btn btn-info">
                        <i class="fas fa-chart-line me-2"></i> Whitelist Analytics
                    </a>
                    {% endif %}
                </div>

                <!-- Analyze Accounts Explanation -->
                <div class="mt-3">
                    <div class="alert alert-light border">
                        <h6 class="alert-heading"><i class="fas fa-info-circle me-2"></i>About Account Analysis</h6>
                        <p class="mb-2">The <strong>Analyze Accounts</strong> button performs intelligent analysis on all collected accounts using your campaign's assigned tags:</p>
                        <ul class="mb-2">
                            <li><strong>Tag Matching:</strong> Applies tag rules to account properties (followers, bio content, account type, etc.)</li>
                            <li><strong>Whitelist Generation:</strong> Creates WhiteListEntry records for accounts that match tag conditions</li>
                            <li><strong>Scoring System:</strong> Evaluates accounts based on tag weights and confidence levels</li>
                            <li><strong>Privilege Assignment:</strong> Sets engagement permissions (DM, follow, comment, etc.) based on tag rules</li>
                        </ul>
                        <p class="mb-0"><small class="text-muted">Analysis results determine which accounts are suitable for engagement activities and what actions are permitted for each account.</small></p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Username Targets Block -->
        <div class="campaign-card mt-4">
            <div class="card-header">
                <h5 class="mb-0 px-2">
                    <i class="fas fa-user text-primary me-2"></i>
                    Username Targets
                </h5>
            </div>
            <div class="card-body">
                {% with username_targets=campaign.username_targets.all %}
                {% if username_targets %}
                <div class="row mb-3">
                    <div class="col-md-6">
                        <div class="search-box">
                            <i class="fas fa-search"></i>
                            <input type="text" class="form-control" id="search-username" placeholder="Search usernames...">
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="filter-container">
                            <select class="form-select" id="filter-status">
                                <option value="">All Status</option>
                                <option value="pending">Pending</option>
                                <option value="processed">Processed</option>
                            </select>
                            <select class="form-select" id="filter-audience-type">
                                <option value="">All Types</option>
                                <option value="profile only">Profile</option>
                                <option value="followers only">Followers</option>
                                <option value="following only">Following</option>
                                <option value="followers and following">Both</option>
                            </select>
                        </div>
                    </div>
                </div>

                <div class="target-list">
                    <div class="table-responsive">
                        <table class="table table-campaigns">
                            <thead>
                                <tr>
                                    <th style="min-width: 150px;">Username</th>
                                    <th style="min-width: 120px;">Audience Type</th>
                                    <th style="min-width: 100px;">Status</th>
                                    <th style="min-width: 100px;">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for target in username_targets %}
                                <tr class="username-item">
                                    <td>
                                        <i class="fas fa-user text-primary me-2"></i>
                                        {{ target.username }}
                                    </td>
                                    <td>
                                        <span class="badge bg-info">{{ target.get_audience_type_display }}</span>
                                    </td>
                                    <td>
                                        {% if target.processed %}
                                        <span class="badge bg-success">Processed</span>
                                        {% else %}
                                        <span class="badge bg-secondary">Pending</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <a href="https://www.instagram.com/{{ target.username }}/" target="_blank" class="btn btn-sm btn-outline-primary" title="View on Instagram">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
                {% else %}
                <div class="empty-state">
                    <div class="empty-state-icon">
                        <i class="fas fa-user text-primary"></i>
                    </div>
                    <div class="empty-state-text">No username targets added yet.</div>
                </div>
                {% endif %}
                {% endwith %}
            </div>
        </div>

        <!-- Location Targets Block -->
        <div class="campaign-card mt-4">
            <div class="card-header">
                <h5 class="mb-0 px-2">
                    <i class="fas fa-map-marker-alt text-danger me-2"></i>
                    Location Targets
                </h5>
            </div>
            <div class="card-body">
                {% with location_targets=campaign.location_targets.all %}
                {% if location_targets %}
                <div class="row mb-3">
                    <div class="col-md-6">
                        <div class="search-box">
                            <i class="fas fa-search"></i>
                            <input type="text" class="form-control" id="search-location" placeholder="Search locations...">
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="filter-container">
                            <select class="form-select" id="filter-country">
                                <option value="">All Countries</option>
                                {% regroup location_targets|dictsort:"country" by country as country_list %}
                                {% for country in country_list %}
                                    <option value="{{ country.grouper }}">{{ country.grouper }}</option>
                                {% endfor %}
                            </select>
                            <select class="form-select" id="filter-city">
                                <option value="">All Cities</option>
                                {% regroup location_targets|dictsort:"city" by city as city_list %}
                                {% for city in city_list %}
                                    <option value="{{ city.grouper }}">{{ city.grouper }}</option>
                                {% endfor %}
                            </select>
                        </div>
                    </div>
                </div>

                <div class="target-list">
                    <div class="table-responsive">
                        <table class="table table-campaigns">
                            <thead>
                                <tr>
                                    <th style="min-width: 150px;">Country</th>
                                    <th style="min-width: 150px;">City</th>
                                    <th style="min-width: 120px;">Location ID</th>
                                    <th style="min-width: 100px;">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for target in location_targets %}
                                <tr class="location-item">
                                    <td>
                                        <i class="fas fa-flag text-primary me-2"></i>
                                        {{ target.country }}
                                    </td>
                                    <td>
                                        <i class="fas fa-city text-secondary me-2"></i>
                                        {{ target.city }}
                                    </td>
                                    <td><code>{{ target.location_id }}</code></td>
                                    <td>
                                        <a href="https://www.instagram.com/explore/locations/{{ target.location_id }}/" target="_blank" class="btn btn-sm btn-outline-primary" title="View on Instagram">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
                {% else %}
                <div class="empty-state">
                    <div class="empty-state-icon">
                        <i class="fas fa-map-marker-alt text-danger"></i>
                    </div>
                    <div class="empty-state-text">No location targets added yet.</div>
                </div>
                {% endif %}
                {% endwith %}
            </div>
        </div>
    </div>
</div>

<style>
    /* Filter styles */
    .filter-container {
        display: flex;
        gap: 10px;
        margin-bottom: 15px;
    }

    .form-select {
        border-radius: 6px;
        padding: 8px 12px;
        border: 1px solid #dee2e6;
        transition: border-color 0.2s ease, box-shadow 0.2s ease;
    }

    .form-select:focus {
        border-color: #3498db;
        box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
    }

    /* Responsive adjustments */
    @media (max-width: 768px) {
        .filter-container {
            flex-direction: column;
        }
    }
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Search and filter functionality for username targets
    const searchUsernameInput = document.getElementById('search-username');
    const filterStatus = document.getElementById('filter-status');
    const filterAudienceType = document.getElementById('filter-audience-type');

    // Function to filter username items
    function filterUsernameItems() {
        const searchTerm = searchUsernameInput ? searchUsernameInput.value.toLowerCase() : '';
        const statusFilter = filterStatus ? filterStatus.value.toLowerCase() : '';
        const audienceTypeFilter = filterAudienceType ? filterAudienceType.value.toLowerCase() : '';

        const items = document.querySelectorAll('.username-item');

        items.forEach(item => {
            const text = item.textContent.toLowerCase();

            // Extract status from the badge text in the "Status" column (3rd column)
            const statusCell = item.querySelector('td:nth-child(3)').textContent.trim().toLowerCase();
            const statusText = statusCell.replace(/.*?(?=\s*[a-z])/i, '').trim();

            // Extract audience type from the badge text in the "Audience Type" column (2nd column)
            const audienceTypeCell = item.querySelector('td:nth-child(2)').textContent.trim().toLowerCase();
            const audienceTypeText = audienceTypeCell.replace(/.*?(?=\s*[a-z])/i, '').trim();

            const matchesSearch = searchTerm === '' || text.includes(searchTerm);
            const matchesStatus = statusFilter === '' || statusText === statusFilter;
            const matchesAudienceType = audienceTypeFilter === '' || audienceTypeText === audienceTypeFilter;

            if (matchesSearch && matchesStatus && matchesAudienceType) {
                item.style.display = '';
            } else {
                item.style.display = 'none';
            }
        });
    }

    // Add event listeners for username targets
    if (searchUsernameInput) {
        searchUsernameInput.addEventListener('keyup', filterUsernameItems);
    }

    if (filterStatus) {
        filterStatus.addEventListener('change', filterUsernameItems);
    }

    if (filterAudienceType) {
        filterAudienceType.addEventListener('change', filterUsernameItems);
    }

    // Search and filter functionality for location targets
    const searchLocationInput = document.getElementById('search-location');
    const filterCountry = document.getElementById('filter-country');
    const filterCity = document.getElementById('filter-city');

    // Function to filter location items
    function filterLocationItems() {
        const searchTerm = searchLocationInput ? searchLocationInput.value.toLowerCase() : '';
        const countryFilter = filterCountry ? filterCountry.value.toLowerCase() : '';
        const cityFilter = filterCity ? filterCity.value.toLowerCase() : '';

        const items = document.querySelectorAll('.location-item');

        items.forEach(item => {
            const text = item.textContent.toLowerCase();
            // Extract just the country name without the icon
            const countryCell = item.querySelector('td:nth-child(1)').textContent.trim().toLowerCase();
            const countryName = countryCell.replace(/.*?(?=\s*[a-z])/i, '').trim();

            // Extract just the city name without the icon
            const cityCell = item.querySelector('td:nth-child(2)').textContent.trim().toLowerCase();
            const cityName = cityCell.replace(/.*?(?=\s*[a-z])/i, '').trim();

            const matchesSearch = searchTerm === '' || text.includes(searchTerm);
            const matchesCountry = countryFilter === '' || countryName === countryFilter;
            const matchesCity = cityFilter === '' || cityName === cityFilter;

            if (matchesSearch && matchesCountry && matchesCity) {
                item.style.display = '';
            } else {
                item.style.display = 'none';
            }
        });
    }

    // Add event listeners
    if (searchLocationInput) {
        searchLocationInput.addEventListener('keyup', filterLocationItems);
    }

    if (filterCountry) {
        filterCountry.addEventListener('change', function() {
            // Update city filter based on selected country
            if (filterCity) {
                const selectedCountry = this.value.toLowerCase();

                // Store all location items for reference
                const locationItems = document.querySelectorAll('.location-item');

                // Clear existing city options except the first one (All Cities)
                while (filterCity.options.length > 1) {
                    filterCity.remove(1);
                }

                // Reset city filter
                filterCity.value = '';

                // If a country is selected, populate city dropdown with cities from that country
                if (selectedCountry) {
                    const citiesInCountry = new Set();

                    // Collect all cities from the selected country
                    locationItems.forEach(item => {
                        const countryCell = item.querySelector('td:nth-child(1)').textContent.trim().toLowerCase();
                        const countryName = countryCell.replace(/.*?(?=\s*[a-z])/i, '').trim();

                        if (countryName === selectedCountry) {
                            const cityCell = item.querySelector('td:nth-child(2)').textContent.trim();
                            const cityName = cityCell.replace(/.*?(?=\s*[a-z])/i, '').trim();
                            citiesInCountry.add(cityName);
                        }
                    });

                    // Add cities to dropdown
                    Array.from(citiesInCountry).sort().forEach(city => {
                        const option = document.createElement('option');
                        option.value = city;
                        option.textContent = city;
                        filterCity.appendChild(option);
                    });
                } else {
                    // If no country selected, add all cities back
                    const allCities = new Set();
                    locationItems.forEach(item => {
                        const cityCell = item.querySelector('td:nth-child(2)').textContent.trim();
                        const cityName = cityCell.replace(/.*?(?=\s*[a-z])/i, '').trim();
                        allCities.add(cityName);
                    });

                    // Add cities to dropdown
                    Array.from(allCities).sort().forEach(city => {
                        const option = document.createElement('option');
                        option.value = city;
                        option.textContent = city;
                        filterCity.appendChild(option);
                    });
                }

                // Filter location items
                filterLocationItems();
            }
        });
    }

    if (filterCity) {
        filterCity.addEventListener('change', filterLocationItems);
    }

    // Remove target buttons have been removed

    // Campaign action buttons (pause, resume, stop)
    const actionButtons = document.querySelectorAll('.campaign-action-btn');
    actionButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();

            const action = this.dataset.action;
            const campaignId = this.dataset.campaignId;
            const campaignName = this.dataset.campaignName;

            let actionText = '';
            let actionUrl = '';

            switch(action) {
                case 'pause':
                    actionText = 'pause';
                    actionUrl = `/campaigns/${campaignId}/pause/`;
                    break;
                case 'resume':
                    actionText = 'resume';
                    actionUrl = `/campaigns/${campaignId}/resume/`;
                    break;
                case 'stop':
                    actionText = 'stop';
                    actionUrl = `/campaigns/${campaignId}/stop/`;
                    break;
                default:
                    return;
            }

            if (confirm(`Are you sure you want to ${actionText} the campaign "${campaignName}"?`)) {
                window.location.href = actionUrl;
            }
        });
    });
});
</script>
{% endblock %}
