{% extends "campaigns/base.html" %}
{% load static %}

{% block extra_css %}
<style>
    /* Location search dropdown styling */
    #location-search-results.show {
        display: block;
        position: absolute;
        width: 100%;
        max-height: 300px;
        overflow-y: auto;
        z-index: 1050;
        border: 1px solid rgba(0,0,0,.15);
        border-radius: 0.25rem;
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    }

    #location-search-results .dropdown-item {
        padding: 0.5rem 1rem;
        clear: both;
        font-weight: 400;
        color: #212529;
        text-align: inherit;
        white-space: normal;
        background-color: transparent;
        border: 0;
        border-bottom: 1px solid #f0f0f0;
    }

    #location-search-results .dropdown-item:last-child {
        border-bottom: none;
    }

    #location-search-results .dropdown-item:hover,
    #location-search-results .dropdown-item:focus {
        color: #16181b;
        text-decoration: none;
        background-color: #f8f9fa;
    }

    #location-search-results .location-main-label {
        font-weight: 500;
        margin-bottom: 2px;
    }

    #location-search-results .location-id {
        color: #6c757d;
        font-size: 0.8em;
    }
</style>
{% endblock %}

{% block title %}{% if form.instance.pk %}Edit Campaign{% else %}Create Campaign{% endif %}{% endblock %}

{% block campaign_content %}
<div class="container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="page-title">{% if form.instance.pk %}Edit Campaign{% else %}Create Campaign{% endif %}</h1>
            <p class="page-subtitle">{% if form.instance.pk %}Update your campaign details{% else %}Set up a new data collection campaign{% endif %}</p>
        </div>
        <div>
            <a href="{% url 'campaigns:campaign_list' %}" class="btn btn-outline-primary btn-icon back-button">
                <i class="fas fa-arrow-left me-1"></i> Back to List
            </a>
        </div>
    </div>

    {% if not form.instance.pk and favorite_campaigns %}
    <div class="alert alert-info mb-4">
        <div class="d-flex align-items-center">
            <div class="flex-grow-1">
                <i class="fas fa-star me-2"></i>
                <strong>Copy from a favorite campaign:</strong> Select a favorite campaign to copy its settings and targets.
            </div>
            <div class="ms-3">
                <div class="dropdown">
                    <button class="btn btn-warning dropdown-toggle" type="button" id="favoriteDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="fas fa-star me-1"></i> Select Favorite
                    </button>
                    <ul class="dropdown-menu" aria-labelledby="favoriteDropdown">
                        {% for campaign in favorite_campaigns %}
                        <li><a class="dropdown-item" href="{% url 'campaigns:campaign_create' %}?copy_from={{ campaign.id }}">{{ campaign.name }}</a></li>
                        {% endfor %}
                    </ul>
                </div>
            </div>
        </div>
    </div>
    {% endif %}
</div>

<div class="container">
    <div class="row">
        <div class="col-lg-8">
            <div class="card campaign-card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-edit me-2"></i>Campaign Information</h5>
                </div>
                <div class="card-body">
                    <form method="post">
                        {% csrf_token %}

                        {% if request.GET.copy_from %}
                        <input type="hidden" name="copy_from_campaign" value="{{ request.GET.copy_from }}">
                        {% endif %}

                        {% if form.non_field_errors %}
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-circle me-2"></i>
                            {% for error in form.non_field_errors %}
                            {{ error }}
                            {% endfor %}
                        </div>
                        {% endif %}

                        <div class="mb-3">
                            <label class="form-label" for="{{ form.name.id_for_label }}">Campaign Name</label>
                            {{ form.name.errors }}
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-tag"></i></span>
                                <input type="text" class="form-control {% if form.name.errors %}is-invalid{% endif %}"
                                    id="{{ form.name.id_for_label }}" name="{{ form.name.html_name }}"
                                    value="{{ form.name.value|default:'' }}" placeholder="Enter campaign name">
                            </div>
                            {% if form.name.help_text %}
                            <small class="form-text text-muted">{{ form.name.help_text }}</small>
                            {% endif %}
                        </div>

                        <div class="mb-3">
                            <label class="form-label" for="{{ form.description.id_for_label }}">Description</label>
                            {{ form.description.errors }}
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-align-left"></i></span>
                                <textarea class="form-control {% if form.description.errors %}is-invalid{% endif %}"
                                        id="{{ form.description.id_for_label }}" name="{{ form.description.html_name }}"
                                        rows="3" placeholder="Describe the purpose of this campaign">{{ form.description.value|default:'' }}</textarea>
                            </div>
                            {% if form.description.help_text %}
                            <small class="form-text text-muted">{{ form.description.help_text }}</small>
                            {% endif %}
                        </div>

                        <!-- Hidden target_type field -->
                        {{ form.target_type }}

                        <!-- Audience Type - only shown when username targeting is enabled -->
                        <div class="row audience-type-container" style="display: none;">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label" for="{{ form.audience_type.id_for_label }}">Audience Type</label>
                                    {{ form.audience_type.errors }}
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="fas fa-users"></i></span>
                                        <select class="form-select {% if form.audience_type.errors %}is-invalid{% endif %}"
                                                id="{{ form.audience_type.id_for_label }}" name="{{ form.audience_type.html_name }}">
                                            {% for value, text in form.fields.audience_type.choices %}
                                            <option value="{{ value }}" {% if form.audience_type.value == value %}selected{% endif %}>
                                                {{ text }}
                                            </option>
                                            {% endfor %}
                                        </select>
                                    </div>
                                    {% if form.audience_type.help_text %}
                                    <small class="form-text text-muted">{{ form.audience_type.help_text }}</small>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <!-- Target Type Section -->
                        <div class="row mb-4">
                            <div class="col-md-12">
                                <!-- Hidden fields for backward compatibility -->
                                <input type="hidden" id="{{ form.enable_location_targeting.id_for_label }}"
                                       name="{{ form.enable_location_targeting.html_name }}" value="true">
                                <input type="hidden" id="{{ form.enable_username_targeting.id_for_label }}"
                                       name="{{ form.enable_username_targeting.html_name }}" value="true">
                            </div>
                        </div>

                        <!-- Location Target Selection -->
                        <div class="card mb-4 location-target-container">
                            <div class="card-header">
                                <h5 class="mb-0"><i class="fas fa-map-marker-alt me-2"></i>Location Targets</h5>
                            </div>
                            <div class="card-body">
                                {{ form.location_targets.errors }}
                                <p class="text-muted mb-3">{{ form.location_targets.help_text }}</p>

                                <!-- Hidden select field that will store the actual values -->
                                {{ form.location_targets }}

                                <!-- Search field with instructions -->
                                <div class="mb-3">
                                    <label class="form-label">Search Locations</label>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="fas fa-search"></i></span>
                                        <input type="text" class="form-control" id="location-search" placeholder="Search by city, country, or location ID..." autocomplete="off">
                                        <button type="button" class="btn btn-primary btn-search" id="location-search-btn">
                                            <i class="fas fa-search"></i> Search
                                        </button>
                                    </div>
                                    <div id="location-search-results" class="dropdown-menu w-100" style="max-height: 300px; overflow-y: auto; z-index: 1050;"></div>
                                    <small class="form-text text-muted">Enter a location name or ID to search. Numeric searches will match location IDs.</small>
                                </div>

                                <!-- Location search results -->
                                {% if search_results %}
                                <div class="list-group mb-3">
                                    {% for location in search_results %}
                                    <div class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                                        <span>{{ location.label }}</span>
                                        <button type="button" class="btn btn-sm btn-primary quick-location"
                                                data-id="{{ location.id }}" data-label="{{ location.label }}">Add</button>
                                    </div>
                                    {% endfor %}
                                </div>
                                {% endif %}



                                <!-- Selected locations display -->
                                <div class="mb-3">
                                    <label class="form-label">Selected Locations</label>
                                    <div id="selected-locations" class="border rounded p-2" style="min-height:100px; max-height:300px; overflow-y:auto;">
                                        <div class="text-muted text-center py-3" id="no-locations-message">No locations selected</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Username Target Input -->
                        <div class="card mb-4 username-target-container">
                            <div class="card-header">
                                <h5 class="mb-0"><i class="fas fa-at me-2"></i>Username Targets</h5>
                            </div>
                            <div class="card-body">
                                <label class="form-label" for="{{ form.usernames.id_for_label }}">Target Usernames</label>
                                {{ form.usernames.errors }}

                                <!-- Username targets table -->
                                <div class="table-responsive mb-3">
                                    <table class="table table-bordered" id="username-targets-table">
                                        <thead>
                                            <tr>
                                                <th style="width: 60%">Username</th>
                                                <th style="width: 35%">Audience Type</th>
                                                <th style="width: 5%"></th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <!-- Rows will be added dynamically -->
                                        </tbody>
                                    </table>
                                </div>

                                <!-- Add username form -->
                                <div class="mb-3">
                                    <div class="row g-2">
                                        <div class="col-md-6">
                                            <div class="input-group">
                                                <span class="input-group-text"><i class="fas fa-at"></i></span>
                                                <input type="text" class="form-control" id="new-username" placeholder="Enter username"
                                                       autocomplete="new-password"
                                                       autocorrect="off"
                                                       autocapitalize="none"
                                                       spellcheck="false"
                                                       data-form-type="other">
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <select class="form-select" id="new-audience-type">
                                                {% for value, label in form.audience_type.field.choices %}
                                                <option value="{{ value }}">{{ label }}</option>
                                                {% endfor %}
                                            </select>
                                        </div>
                                        <div class="col-md-2">
                                            <button type="button" id="add-username-btn" class="btn btn-primary btn-add w-100">
                                                <i class="fas fa-plus"></i> Add
                                            </button>
                                        </div>
                                    </div>
                                </div>

                                <!-- Username add help removed -->

                                <!-- Bulk import option -->
                                <div class="mb-3">
                                    <button type="button" class="btn btn-outline-secondary" data-bs-toggle="collapse" data-bs-target="#bulk-import">
                                        <i class="fas fa-file-import"></i> Bulk Import
                                    </button>
                                    <div class="collapse mt-2" id="bulk-import">
                                        <div class="card card-body">
                                            <p class="text-muted small">Enter one username per line. You can specify audience type by adding a colon followed by the type (profile, followers, following, both).</p>
                                            <p class="text-muted small">Example: username1:followers</p>
                                            <textarea class="form-control mb-2" id="bulk-usernames" rows="5" placeholder="username1:followers&#10;username2:following&#10;username3:profile"></textarea>
                                            <button type="button" class="btn btn-sm btn-primary" id="import-usernames-btn">Import</button>
                                        </div>
                                    </div>
                                </div>

                                <!-- Hidden field to store the JSON data -->
                                <input type="hidden" id="{{ form.usernames.id_for_label }}" name="{{ form.usernames.html_name }}" value="{{ form.usernames.value|default:'' }}">

                                <small class="form-text text-muted">Add usernames to target with specific audience types.</small>
                            </div>
                        </div>

                        <!-- Scheduled Start and End fields removed -->

                        <div class="btn-action-group">
                            <button type="submit" class="btn btn-primary" id="submit-campaign-form">
                                <i class="fas {% if form.instance.pk %}fa-save{% else %}fa-plus-circle{% endif %} me-1"></i>
                                {% if form.instance.pk %}Update Campaign{% else %}Create Campaign{% endif %}
                            </button>
                            <a href="{% url 'campaigns:campaign_list' %}" class="btn btn-secondary">
                                <i class="fas fa-times me-1"></i> Cancel
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="card campaign-card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-info-circle me-2"></i>Help & Information</h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-info mb-3">
                        <i class="fas fa-lightbulb me-2"></i>
                        <strong>Target Types:</strong> You can use both location and username targeting simultaneously.
                        Add targets to either or both sections below.
                    </div>

                    <div class="alert alert-info mb-3">
                        <i class="fas fa-lightbulb me-2"></i>
                        <strong>Target Type</strong> determines the initial source of Instagram accounts:
                        <ul class="mb-0 mt-2">
                            <li><strong>Location Based:</strong> Target accounts that post at specific geographic locations</li>
                            <li><strong>Username Based:</strong> Target specific Instagram accounts directly by username</li>
                        </ul>
                    </div>

                    <div class="alert alert-info mb-3">
                        <i class="fas fa-lightbulb me-2"></i>
                        <strong>Audience Type</strong> is only relevant for Username Based targeting and determines which accounts to collect:
                        <ul class="mb-0 mt-2">
                            <li><strong>Profile Only:</strong> Only collect data from the specified usernames</li>
                            <li><strong>Followers Only:</strong> Collect data from accounts that follow the target usernames</li>
                            <li><strong>Following Only:</strong> Collect data from accounts that the target usernames follow</li>
                            <li><strong>Both:</strong> Collect data from both followers and following of the target usernames</li>
                        </ul>
                    </div>

                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        For Location Based targeting, the Audience Type setting is not used as we're collecting usernames from location posts.
                    </div>

                    <!-- Scheduled Start info removed -->
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    // Extremely simple script with minimal functionality
    document.addEventListener('DOMContentLoaded', function() {
        // Basic elements
        const locationTargetsSelect = document.getElementById('{{ form.location_targets.id_for_label }}');
        const selectedLocationsContainer = document.getElementById('selected-locations');
        const noLocationsMessage = document.getElementById('no-locations-message');
        const usernameTargetsTable = document.getElementById('username-targets-table').querySelector('tbody');
        const usernamesHiddenInput = document.getElementById('{{ form.usernames.id_for_label }}');

        // Function to add a location
        function addLocation(id, label) {
            // Check if already selected
            for (let i = 0; i < locationTargetsSelect.options.length; i++) {
                if (locationTargetsSelect.options[i].value === id) {
                    return; // Already exists
                }
            }

            // Add to select
            const option = document.createElement('option');
            option.value = id;
            option.textContent = label;
            option.selected = true;
            locationTargetsSelect.appendChild(option);

            // Add visual badge with location ID
            const badge = document.createElement('div');
            badge.className = 'badge bg-primary me-2 mb-2 p-2 d-flex align-items-center justify-content-between';
            badge.style.minWidth = '200px';

            // Create badge content with location name and ID
            const badgeContent = document.createElement('span');
            badgeContent.className = 'd-flex flex-column align-items-start';

            const locationName = document.createElement('span');
            locationName.textContent = label;

            const locationId = document.createElement('small');
            locationId.className = 'location-id-badge';
            locationId.textContent = `ID: ${id}`;
            locationId.style.fontSize = '0.75em';
            locationId.style.opacity = '0.8';

            badgeContent.appendChild(locationName);
            badgeContent.appendChild(locationId);

            // Create remove button
            const removeButton = document.createElement('button');
            removeButton.type = 'button';
            removeButton.className = 'btn btn-sm btn-danger ms-2 py-0 px-1';
            removeButton.setAttribute('aria-label', 'Remove');
            removeButton.setAttribute('title', 'Delete this location');
            removeButton.innerHTML = '<i class="fas fa-times"></i>';

            badge.appendChild(badgeContent);
            badge.appendChild(removeButton);

            // Add remove functionality
            badge.querySelector('.btn-danger').onclick = function() {
                // Confirm deletion
                if (confirm(`Are you sure you want to remove "${label}" from the location targets?`)) {
                    // Remove from select
                    for (let i = 0; i < locationTargetsSelect.options.length; i++) {
                        if (locationTargetsSelect.options[i].value === id) {
                            locationTargetsSelect.remove(i);
                            break;
                        }
                    }

                    // Remove badge
                    badge.remove();

                    // Show message if needed
                    if (locationTargetsSelect.options.length === 0) {
                        noLocationsMessage.style.display = 'block';
                    }
                }
            };

            selectedLocationsContainer.appendChild(badge);
            noLocationsMessage.style.display = 'none';
        }

        // Add event listeners to quick location buttons
        document.querySelectorAll('.quick-location').forEach(button => {
            button.addEventListener('click', function() {
                const id = this.getAttribute('data-id');
                const label = this.getAttribute('data-label');
                addLocation(id, label);
            });
        });

        // Add username button functionality
        document.getElementById('add-username-btn').addEventListener('click', function(e) {
            // Prevent form submission if inside a form
            if (e && e.preventDefault) {
                e.preventDefault();
            }

            const usernameInput = document.getElementById('new-username');
            const audienceTypeSelect = document.getElementById('new-audience-type');

            const username = usernameInput.value.trim();
            const audienceType = audienceTypeSelect.value;

            if (!username) {
                // Don't show alert if empty, just return silently
                return;
            }

            // Check if exists
            const existingIndex = usernameTargets.findIndex(t =>
                t.username.toLowerCase() === username.toLowerCase());

            if (existingIndex !== -1) {
                usernameTargets[existingIndex].audienceType = audienceType;
            } else {
                usernameTargets.push({
                    username: username,
                    audienceType: audienceType
                });
            }

            // Update table and clear input
            updateUsernameTable();
            usernameInput.value = '';
            usernameInput.focus();
        });

        // Add event listener for Enter key in username input
        document.getElementById('new-username').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                e.preventDefault(); // Prevent form submission
                document.getElementById('add-username-btn').click();
            }
        });

        // Initialize username targets
        let usernameTargets = [];

        // Try to parse initial username data
        try {
            const initialValue = usernamesHiddenInput.value.trim();
            if (initialValue) {
                usernameTargets = JSON.parse(initialValue);
                updateUsernameTable();
            }
        } catch (e) {
            console.error('Error parsing initial usernames:', e);
        }

        // Function to update username table
        function updateUsernameTable() {
            // Update hidden input
            usernamesHiddenInput.value = JSON.stringify(usernameTargets);

            // Clear table
            usernameTargetsTable.innerHTML = '';

            // Add rows
            usernameTargets.forEach((target, index) => {
                const row = document.createElement('tr');

                // Username cell
                const usernameCell = document.createElement('td');
                usernameCell.textContent = target.username;
                row.appendChild(usernameCell);

                // Audience type cell
                const audienceTypeCell = document.createElement('td');
                const select = document.createElement('select');
                select.className = 'form-select form-select-sm';

                // Add options
                {% for value, label in form.audience_type.field.choices %}
                (function() {
                    const optEl = document.createElement('option');
                    optEl.value = '{{ value }}';
                    optEl.textContent = '{{ label }}';
                    if (target.audienceType === '{{ value }}') {
                        optEl.selected = true;
                    }
                    select.appendChild(optEl);
                })();
                {% endfor %}

                // Handle change
                select.onchange = function() {
                    target.audienceType = this.value;
                    usernamesHiddenInput.value = JSON.stringify(usernameTargets);
                };

                audienceTypeCell.appendChild(select);
                row.appendChild(audienceTypeCell);

                // Action cell
                const actionCell = document.createElement('td');
                const removeBtn = document.createElement('button');
                removeBtn.type = 'button';
                removeBtn.className = 'btn btn-sm btn-danger';
                removeBtn.innerHTML = '<i class="fas fa-times"></i>';

                removeBtn.onclick = function() {
                    usernameTargets.splice(index, 1);
                    updateUsernameTable();
                };

                actionCell.appendChild(removeBtn);
                row.appendChild(actionCell);

                usernameTargetsTable.appendChild(row);
            });
        }

        // Add event listener for import button
        document.getElementById('import-usernames-btn').addEventListener('click', function() {
            const textarea = document.getElementById('bulk-usernames');
            const text = textarea.value.trim();

            if (!text) return;

            const lines = text.split('\n');

            lines.forEach(line => {
                line = line.trim();
                if (!line) return;

                // Check for audience type
                const parts = line.split(':');
                const username = parts[0].trim();
                let audienceType = 'profile'; // Default

                if (parts.length > 1) {
                    const type = parts[1].trim().toLowerCase();
                    if (['profile', 'followers', 'following', 'both'].includes(type)) {
                        audienceType = type;
                    }
                }

                // Add to targets
                const existingIndex = usernameTargets.findIndex(t =>
                    t.username.toLowerCase() === username.toLowerCase());

                if (existingIndex !== -1) {
                    usernameTargets[existingIndex].audienceType = audienceType;
                } else {
                    usernameTargets.push({
                        username: username,
                        audienceType: audienceType
                    });
                }
            });

            // Update table and clear textarea
            updateUsernameTable();
            textarea.value = '';
        });

        // Add event listener for form submission
        document.querySelector('form').addEventListener('submit', function(e) {
            // Ensure all location targets are selected in the hidden select field
            const locationTargetsSelect = document.getElementById('{{ form.location_targets.id_for_label }}');

            // Log for debugging
            console.log('Location targets before submission:', locationTargetsSelect.options.length);

            // Make sure all options are selected
            for (let i = 0; i < locationTargetsSelect.options.length; i++) {
                locationTargetsSelect.options[i].selected = true;
            }

            // Log after selection
            console.log('Location targets after selection:',
                Array.from(locationTargetsSelect.selectedOptions).map(opt => opt.value));
        });

        // Keep the click handler for the submit button as well for redundancy
        document.getElementById('submit-campaign-form').addEventListener('click', function(e) {
            // Ensure all location targets are selected in the hidden select field
            const locationTargetsSelect = document.getElementById('{{ form.location_targets.id_for_label }}');

            // Make sure all options are selected
            for (let i = 0; i < locationTargetsSelect.options.length; i++) {
                locationTargetsSelect.options[i].selected = true;
            }
        });

        // Initialize selected locations
        for (let i = 0; i < locationTargetsSelect.options.length; i++) {
            const option = locationTargetsSelect.options[i];
            const id = option.value;
            const label = option.textContent;

            // Add visual badge with location ID
            const badge = document.createElement('div');
            badge.className = 'badge bg-primary me-2 mb-2 p-2 d-flex align-items-center justify-content-between';
            badge.style.minWidth = '200px';

            // Create badge content with location name and ID
            const badgeContent = document.createElement('span');
            badgeContent.className = 'd-flex flex-column align-items-start';

            const locationName = document.createElement('span');
            locationName.textContent = label;

            const locationId = document.createElement('small');
            locationId.className = 'location-id-badge';
            locationId.textContent = `ID: ${id}`;
            locationId.style.fontSize = '0.75em';
            locationId.style.opacity = '0.8';

            badgeContent.appendChild(locationName);
            badgeContent.appendChild(locationId);

            // Create remove button
            const removeButton = document.createElement('button');
            removeButton.type = 'button';
            removeButton.className = 'btn btn-sm btn-danger ms-2 py-0 px-1';
            removeButton.setAttribute('aria-label', 'Remove');
            removeButton.setAttribute('title', 'Delete this location');
            removeButton.innerHTML = '<i class="fas fa-times"></i>';

            badge.appendChild(badgeContent);
            badge.appendChild(removeButton);

            // Add remove functionality
            badge.querySelector('.btn-danger').onclick = function() {
                // Confirm deletion
                if (confirm(`Are you sure you want to remove "${label}" from the location targets?`)) {
                    // Remove from select
                    for (let j = 0; j < locationTargetsSelect.options.length; j++) {
                        if (locationTargetsSelect.options[j].value === id) {
                            locationTargetsSelect.remove(j);
                            break;
                        }
                    }

                    // Remove badge
                    badge.remove();

                    // Show message if needed
                    if (locationTargetsSelect.options.length === 0) {
                        noLocationsMessage.style.display = 'block';
                    }
                }
            };

            selectedLocationsContainer.appendChild(badge);
        }

        // Hide "no locations" message if needed
        if (locationTargetsSelect.options.length > 0) {
            noLocationsMessage.style.display = 'none';
        }

        // Location search autocomplete
        const locationSearch = document.getElementById('location-search');
        const locationSearchResults = document.getElementById('location-search-results');
        const locationSearchBtn = document.getElementById('location-search-btn');
        let searchTimeout;

        // Function to perform location search
        function performLocationSearch() {
            const query = locationSearch.value.trim();
            if (!query) return;

            // Show loading indicator
            locationSearchResults.innerHTML = '<span class="dropdown-item-text">Searching...</span>';
            locationSearchResults.classList.add('show');

            // Log the search request for debugging
            console.log(`Searching for locations with query: ${query}`);

            // Make API request
            fetch(`/campaigns/api/locations/search/?q=${encodeURIComponent(query)}&limit=10`)
                .then(response => {
                    console.log('Search response status:', response.status);
                    return response.json();
                })
                .then(data => {
                    // Log the response data for debugging
                    console.log('Search results:', data);

                    // Clear previous results
                    locationSearchResults.innerHTML = '';

                    if (data.results && data.results.length > 0) {
                        // Add results to dropdown
                        data.results.forEach(location => {
                            const item = document.createElement('a');
                            item.className = 'dropdown-item';
                            item.href = '#';

                            // Create a more structured display with the location ID
                            const mainLabel = document.createElement('div');
                            mainLabel.className = 'location-main-label';
                            mainLabel.textContent = location.display_label || location.label.split(' (ID:')[0];

                            const idLabel = document.createElement('small');
                            idLabel.className = location.highlight_id ? 'text-primary location-id fw-bold' : 'text-muted location-id';
                            idLabel.textContent = `ID: ${location.id}`;
                            idLabel.style.display = 'block';
                            idLabel.style.fontSize = '0.8em';

                            // If this is a highlighted ID match, add a badge
                            if (location.highlight_id) {
                                idLabel.style.backgroundColor = '#e7f3ff';
                                idLabel.style.padding = '1px 4px';
                                idLabel.style.borderRadius = '3px';
                            }

                            item.appendChild(mainLabel);
                            item.appendChild(idLabel);

                            item.addEventListener('click', function(e) {
                                e.preventDefault();
                                // Use display_label for the badge if available, otherwise use the main part of the label
                                const displayLabel = location.display_label || location.label.split(' (ID:')[0];
                                addLocation(location.id, displayLabel);
                                locationSearch.value = '';
                                locationSearchResults.classList.remove('show');
                            });

                            locationSearchResults.appendChild(item);
                        });

                        // Show dropdown
                        locationSearchResults.classList.add('show');
                    } else {
                        // No results
                        const item = document.createElement('span');
                        item.className = 'dropdown-item-text text-muted';
                        item.textContent = 'No locations found';
                        locationSearchResults.appendChild(item);
                        locationSearchResults.classList.add('show');
                    }
                })
                .catch(error => {
                    console.error('Error searching locations:', error);
                    // Show error message in dropdown
                    locationSearchResults.innerHTML = '<span class="dropdown-item-text text-danger">Error searching locations</span>';
                    locationSearchResults.classList.add('show');
                });
        }

        if (locationSearch) {
            // Add event listener for input changes (with debounce)
            locationSearch.addEventListener('input', function() {
                const query = this.value.trim();

                // Clear previous timeout
                clearTimeout(searchTimeout);

                // Hide results if query is empty
                if (!query) {
                    locationSearchResults.classList.remove('show');
                    return;
                }

                // Set a timeout to avoid making too many requests
                searchTimeout = setTimeout(performLocationSearch, 300); // 300ms delay
            });

            // Add event listener for search button
            if (locationSearchBtn) {
                locationSearchBtn.addEventListener('click', performLocationSearch);
            }

            // Add event listener for Enter key
            locationSearch.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    e.preventDefault(); // Prevent form submission
                    performLocationSearch();
                }
            });

            // Hide dropdown when clicking outside
            document.addEventListener('click', function(e) {
                if (!locationSearch.contains(e.target) &&
                    !locationSearchResults.contains(e.target) &&
                    !locationSearchBtn.contains(e.target)) {
                    locationSearchResults.classList.remove('show');
                }
            });

            // Show dropdown when focusing on search input
            locationSearch.addEventListener('focus', function() {
                // If there's a value and we have results, show the dropdown
                if (this.value.trim() && locationSearchResults.children.length > 0) {
                    locationSearchResults.classList.add('show');
                }
                // If there's a value but no search has been performed yet, trigger a search
                else if (this.value.trim() && locationSearchResults.children.length === 0) {
                    performLocationSearch();
                }
            });
        }
    });
</script>
{% endblock %}
