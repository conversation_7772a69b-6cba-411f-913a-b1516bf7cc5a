"""
Forms for account-related functionality in the campaigns app.
"""
from django import forms
from django.db.models import Q
from instagram.models import Accounts


class AccountFilterForm(forms.Form):
    """
    Form for filtering accounts in the campaign accounts view.
    """
    # Search fields
    search = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'Search by username, full name, or bio...',
            'aria-label': 'Search accounts'
        })
    )

    # Follower range
    min_followers = forms.IntegerField(
        required=False,
        widget=forms.NumberInput(attrs={
            'class': 'form-control',
            'placeholder': 'Min',
            'min': '0'
        })
    )

    max_followers = forms.IntegerField(
        required=False,
        widget=forms.NumberInput(attrs={
            'class': 'form-control',
            'placeholder': 'Max',
            'min': '0'
        })
    )

    # Following range
    min_following = forms.IntegerField(
        required=False,
        widget=forms.NumberInput(attrs={
            'class': 'form-control',
            'placeholder': 'Min',
            'min': '0'
        })
    )

    max_following = forms.IntegerField(
        required=False,
        widget=forms.NumberInput(attrs={
            'class': 'form-control',
            'placeholder': 'Max',
            'min': '0'
        })
    )

    # Posts range
    min_posts = forms.IntegerField(
        required=False,
        widget=forms.NumberInput(attrs={
            'class': 'form-control',
            'placeholder': 'Min',
            'min': '0'
        })
    )

    max_posts = forms.IntegerField(
        required=False,
        widget=forms.NumberInput(attrs={
            'class': 'form-control',
            'placeholder': 'Max',
            'min': '0'
        })
    )

    # Account type
    ACCOUNT_TYPE_CHOICES = [
        ('', 'All Types'),
        ('personal', 'Personal'),
        ('business', 'Business')
    ]

    account_type = forms.ChoiceField(
        choices=ACCOUNT_TYPE_CHOICES,
        required=False,
        widget=forms.Select(attrs={
            'class': 'form-select'
        })
    )

    # Whitelist status
    WHITELIST_CHOICES = [
        ('', 'All Accounts'),
        ('yes', 'Whitelisted'),
        ('no', 'Not Whitelisted')
    ]

    whitelist_status = forms.ChoiceField(
        choices=WHITELIST_CHOICES,
        required=False,
        widget=forms.Select(attrs={
            'class': 'form-select'
        })
    )

    # Tags (will be populated dynamically in the view)
    tags = forms.MultipleChoiceField(
        required=False,
        widget=forms.SelectMultiple(attrs={
            'class': 'form-select',
            'size': '4'
        })
    )

    # Sort options
    SORT_CHOICES = [
        ('username', 'Username (A-Z)'),
        ('-username', 'Username (Z-A)'),
        ('full_name', 'Full Name (A-Z)'),
        ('-full_name', 'Full Name (Z-A)'),
        ('followers', 'Followers (Low to High)'),
        ('-followers', 'Followers (High to Low)'),
        ('following', 'Following (Low to High)'),
        ('-following', 'Following (High to Low)'),
        ('number_of_posts', 'Posts (Low to High)'),
        ('-number_of_posts', 'Posts (High to Low)')
    ]

    sort_by = forms.ChoiceField(
        choices=SORT_CHOICES,
        required=False,
        initial='-followers',
        widget=forms.Select(attrs={
            'class': 'form-select'
        })
    )

    def filter_queryset(self, queryset):
        """
        Apply filters to the queryset based on form data.
        """
        cleaned_data = self.cleaned_data

        # Apply search filter
        search_term = cleaned_data.get('search')
        if search_term:
            queryset = queryset.filter(
                Q(username__icontains=search_term)
                | Q(full_name__icontains=search_term)
                | Q(bio__icontains=search_term)
            )

        # Apply follower range filters
        min_followers = cleaned_data.get('min_followers')
        if min_followers is not None:
            queryset = queryset.filter(followers__gte=min_followers)

        max_followers = cleaned_data.get('max_followers')
        if max_followers is not None:
            queryset = queryset.filter(followers__lte=max_followers)

        # Apply following range filters
        min_following = cleaned_data.get('min_following')
        if min_following is not None:
            queryset = queryset.filter(following__gte=min_following)

        max_following = cleaned_data.get('max_following')
        if max_following is not None:
            queryset = queryset.filter(following__lte=max_following)

        # Apply posts range filters
        min_posts = cleaned_data.get('min_posts')
        if min_posts is not None:
            queryset = queryset.filter(number_of_posts__gte=min_posts)

        max_posts = cleaned_data.get('max_posts')
        if max_posts is not None:
            queryset = queryset.filter(number_of_posts__lte=max_posts)

        # Apply account type filter
        account_type = cleaned_data.get('account_type')
        if account_type:
            queryset = queryset.filter(account_type=account_type)

        # Apply whitelist status filter
        whitelist_status = cleaned_data.get('whitelist_status')
        if whitelist_status == 'yes':
            queryset = queryset.filter(whitelistentry__isnull=False)
        elif whitelist_status == 'no':
            queryset = queryset.filter(whitelistentry__isnull=True)

        # Apply tag filters
        selected_tags = cleaned_data.get('tags', [])
        if selected_tags:
            from django.contrib.contenttypes.models import ContentType
            from instagram.models import CustomTaggedItem

            content_type = ContentType.objects.get_for_model(Accounts)
            for tag in selected_tags:
                tag_filter = CustomTaggedItem.objects.filter(
                    content_type=content_type,
                    tag__name=tag,
                    object_id__in=queryset.values_list('username', flat=True)
                ).values_list('object_id', flat=True)

                queryset = queryset.filter(username__in=tag_filter)

        # Apply sorting
        sort_by = cleaned_data.get('sort_by')
        if sort_by:
            queryset = queryset.order_by(sort_by)
        else:
            queryset = queryset.order_by('-followers')  # Default sorting

        return queryset
